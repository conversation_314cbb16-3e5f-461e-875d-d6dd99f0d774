#!/bin/bash

# ULTRA-ERP Presentation Page - Install Dependencies
# This script installs node_modules using Docker

set -e

echo "🔧 Installing dependencies via Docker..."
echo "This may take a few minutes on first run..."

# Build Docker image if it doesn't exist
if ! docker images | grep -q "ultra-erp-presentation-page"; then
    echo "📦 Building Docker image..."
    docker-compose build ultra-erp-dev
fi

# Install dependencies
echo "📥 Installing npm packages..."
docker-compose run --rm ultra-erp-dev npm install

echo "✅ Dependencies installed successfully!"
echo "You can now run './scripts/start.sh' to start the development server."
