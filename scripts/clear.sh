#!/bin/bash

# ULTRA-ERP Presentation Page - Clear Build Artifacts
# This script removes dist/ and node_modules/ folders

set -e

echo "🧹 Clearing build artifacts and dependencies..."

# Remove dist folder
if [ -d "dist" ]; then
    echo "🗑️  Removing dist/ folder..."
    sudo rm -rf dist/ 2>/dev/null || docker run --rm -v "$(pwd):/app" alpine rm -rf /app/dist
    echo "✅ dist/ folder removed"
else
    echo "ℹ️  dist/ folder not found"
fi

# Remove node_modules folder
if [ -d "node_modules" ]; then
    echo "🗑️  Removing node_modules/ folder..."
    sudo rm -rf node_modules/ 2>/dev/null || docker run --rm -v "$(pwd):/app" alpine rm -rf /app/node_modules
    echo "✅ node_modules/ folder removed"
else
    echo "ℹ️  node_modules/ folder not found"
fi

# Remove Docker containers and images
echo "🐳 Cleaning up Docker containers and images..."
docker-compose down --rmi all --volumes --remove-orphans 2>/dev/null || true

echo "✅ All build artifacts and dependencies cleared!"
echo "Run './scripts/install.sh' to reinstall dependencies."
