#!/bin/bash

# ULTRA-ERP Presentation Page - Build Production Assets
# This script builds optimized production assets in dist/ folder

set -e

echo "🏗️  Building production assets..."

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "⚠️  node_modules not found. Installing dependencies first..."
    ./scripts/install.sh
fi

# Build production assets
echo "📦 Creating optimized production build..."
docker-compose run --rm ultra-erp-build

echo "✅ Production build completed successfully!"
echo "📁 Built assets are available in the 'dist/' folder."
echo ""
echo "Build contents:"
ls -la dist/
