#!/bin/bash

# ULTRA-ERP Presentation Page - Start Development Server
# This script starts the webpack dev server with hot reloading

set -e

echo "🚀 Starting ULTRA-ERP development server..."

# Check if node_modules exists, if not install dependencies
if [ ! -d "node_modules" ]; then
    echo "⚠️  node_modules not found. Installing dependencies first..."
    ./scripts/install.sh
fi

# Build Docker image if it doesn't exist
if ! docker images | grep -q "ultra-erp-presentation-page"; then
    echo "📦 Building Docker image..."
    docker-compose build ultra-erp-dev
fi

echo "🌐 Starting development server..."
echo "📍 Server will be available at: http://localhost:3000"
echo "🔥 Hot reloading is enabled - changes will be reflected automatically"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the development server
docker-compose up ultra-erp-dev
