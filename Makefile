# ULTRA-ERP Presentation Page - Development Commands

.PHONY: help install start build clean stop logs shell

# Default target
help:
	@echo "ULTRA-ERP Presentation Page - Available Commands:"
	@echo ""
	@echo "  make install    - Install dependencies via Docker"
	@echo "  make start      - Start development server with hot reload"
	@echo "  make build      - Build production assets"
	@echo "  make clean      - Clean build artifacts and cache"
	@echo "  make stop       - Stop all running containers"
	@echo "  make logs       - Show container logs"
	@echo "  make shell      - Open shell in development container"
	@echo ""

# Install dependencies
install:
	@echo "Installing dependencies via Docker..."
	docker-compose run --rm ultra-erp-dev npm install

# Start development server
start:
	@echo "Starting development server..."
	@echo "The application will be available at http://localhost:3000"
	docker-compose up ultra-erp-dev

# Build production assets
build:
	@echo "Building production assets..."
	docker-compose run --rm ultra-erp-build

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	docker-compose run --rm ultra-erp-dev npm run clean

# Stop all containers
stop:
	@echo "Stopping all containers..."
	docker-compose down

# Show logs
logs:
	docker-compose logs -f ultra-erp-dev

# Open shell in development container
shell:
	docker-compose run --rm ultra-erp-dev sh

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker-compose build

# Remove all containers and images
docker-clean:
	@echo "Removing containers and images..."
	docker-compose down --rmi all --volumes --remove-orphans
