{"name": "ultra-erp-presentation-page", "version": "1.0.0", "description": "Modern presentation page for ULTRA-ERP system with webpack build tooling", "main": "index.js", "scripts": {"start": "webpack serve --mode development --config webpack.config.js --host 0.0.0.0", "install": "docker-compose run --rm ultra-erp-dev npm install", "clean": "rimraf dist && rimraf node_modules/.cache", "build": "npm run clean && webpack --mode production --config webpack.config.js", "build:dev": "webpack --mode development --config webpack.config.js", "docker:start": "docker-compose up ultra-erp-dev", "docker:build": "docker-compose run --rm ultra-erp-build", "docker:install": "docker-compose run --rm ultra-erp-dev npm install", "docker:clean": "docker-compose run --rm ultra-erp-dev npm run clean"}, "keywords": ["erp", "symfony", "webpack", "tailwindcss", "presentation"], "author": "ULTRA-ERP Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "autoprefixer": "^10.4.16", "babel-loader": "^9.1.3", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "rimraf": "^5.0.5", "style-loader": "^3.3.3", "tailwindcss": "^3.3.5", "terser-webpack-plugin": "^5.3.9", "url-loader": "^4.1.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.2", "aos": "^2.3.4", "core-js": "^3.33.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}