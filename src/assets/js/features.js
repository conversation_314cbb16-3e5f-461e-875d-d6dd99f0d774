// Features functionality
function toggleFeatureCard(card) {
    const details = card.querySelector('.feature-details');
    const summary = card.querySelector('.feature-summary');
    const toggle = card.querySelector('.feature-toggle');
    const icon = toggle.querySelector('i');
    const isExpanded = details.style.display !== 'none';

    if (isExpanded) {
        // Collapse
        details.style.display = 'none';
        summary.style.display = 'block';
        toggle.querySelector('span').textContent = 'Klikněte pro více informací';
        icon.style.transform = 'rotate(0deg)';
        card.classList.remove('expanded');
    } else {
        // Expand
        details.style.display = 'block';
        summary.style.display = 'none';
        toggle.querySelector('span').textContent = 'Klikněte pro méně informací';
        icon.style.transform = 'rotate(180deg)';
        card.classList.add('expanded');
    }
}

// Smooth scroll to features section
function scrollToFeatures() {
    const featuresSection = document.getElementById('features');
    if (!featuresSection) return;

    // Get the actual position of the features section
    const rect = featuresSection.getBoundingClientRect();
    const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Calculate target position accounting for the scroll-mt-20 offset (80px)
    const targetPosition = currentScrollTop + rect.top - 80;

    window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
    });
}

export function initFeatures() {
    // Make functions globally available
    window.toggleFeatureCard = toggleFeatureCard;
    window.scrollToFeatures = scrollToFeatures;
    
    // Update the "Prozkoumat vlastnosti" button
    const exploreButton = document.querySelector('a[href="#features"]');
    if (exploreButton) {
        exploreButton.addEventListener('click', function(e) {
            e.preventDefault();
            scrollToFeatures();
        });
    }
}
