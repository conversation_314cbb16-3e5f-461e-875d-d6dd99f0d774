export function initNavigation() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Mobile menu toggle
    const mobileMenuButton = document.querySelector('.md\\:hidden button');
    if (mobileMenuButton) {
        const mobileMenu = document.createElement('div');
        mobileMenu.className = 'md:hidden bg-gray-900 shadow-lg absolute top-full left-0 right-0 z-40 border-t border-gray-800';
        mobileMenu.innerHTML = `
            <div class="px-4 py-2 space-y-2">
                <a href="#features" class="block py-2 text-gray-300 hover:text-cyan-400">Vlastnosti</a>
                <a href="#screenshots" class="block py-2 text-gray-300 hover:text-cyan-400">Screenshoty</a>
                <a href="#technology" class="block py-2 text-gray-300 hover:text-cyan-400">Technologie</a>
                <a href="#pricing" class="block py-2 text-gray-300 hover:text-cyan-400">Ceník</a>
                <a href="#contact" class="block py-2 text-gray-300 hover:text-cyan-400">Kontakt</a>
            </div>
        `;

        let mobileMenuOpen = false;
        mobileMenuButton.addEventListener('click', () => {
            if (!mobileMenuOpen) {
                mobileMenuButton.parentElement.parentElement.appendChild(mobileMenu);
                mobileMenuOpen = true;
            } else {
                mobileMenu.remove();
                mobileMenuOpen = false;
            }
        });
    }

    // Navbar background and hide/show on scroll
    let lastScrollY = window.scrollY;
    let ticking = false;

    function updateNavbar() {
        const navbar = document.querySelector('nav');
        if (!navbar) return;

        const currentScrollY = window.scrollY;

        // Change background opacity based on scroll position
        if (currentScrollY > 50) {
            navbar.classList.add('bg-gray-900/95');
            navbar.classList.remove('bg-gray-900/90');
        } else {
            navbar.classList.add('bg-gray-900/90');
            navbar.classList.remove('bg-gray-900/95');
        }

        // Hide/show navbar based on scroll direction
        // Get hero section height to determine when to start hiding navbar
        const heroSection = document.querySelector('.gradient-bg.min-h-screen');
        const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;

        if (currentScrollY > heroHeight * 0.8) { // Start hiding when 80% through hero section
            if (currentScrollY > lastScrollY && currentScrollY > heroHeight * 0.9) {
                // Scrolling down & past 90% of hero section - hide navbar
                navbar.style.transform = 'translateY(-100%)';
                navbar.style.transition = 'transform 0.3s ease-in-out';
            } else if (currentScrollY < lastScrollY) {
                // Scrolling up - show navbar
                navbar.style.transform = 'translateY(0)';
                navbar.style.transition = 'transform 0.3s ease-in-out';
            }
        } else {
            // Always show navbar when in hero section
            navbar.style.transform = 'translateY(0)';
        }

        lastScrollY = currentScrollY;
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateNavbar);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);
}
