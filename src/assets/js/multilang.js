// Multilang functionality for Ultra ERP presentation page

// Language data
const translations = {
    en: {
        // Navigation
        'nav-features': 'Features',
        'nav-screenshots': 'Screenshots', 
        'nav-technology': 'Technology',
        'nav-contact': 'Contact',
        
        // Hero section
        'hero-title': 'Revolutionary Modular ERP System',
        'hero-subtitle': 'Next-generation enterprise solution combining artificial intelligence power with enterprise-grade security and flexibility',
        'hero-cta-primary': 'Contact Us',
        'hero-cta-secondary': 'Free Consultation',
        
        // Features section
        'features-title': 'Key Features',
        'features-subtitle': 'Discover what makes Ultra ERP unique',
        
        'feature-ai-title': 'AI Integration',
        'feature-ai-desc': 'Advanced artificial intelligence for process automation and intelligent analytics',
        
        'feature-modular-title': 'Modular Architecture',
        'feature-modular-desc': 'Flexible system that adapts to your business needs',
        
        'feature-security-title': 'Enterprise Security',
        'feature-security-desc': 'Bank-level security with advanced encryption and access control',
        
        'feature-cloud-title': 'Cloud & On-Premise',
        'feature-cloud-desc': 'Deploy in cloud or on your own infrastructure',
        
        'feature-api-title': 'Modern API',
        'feature-api-desc': 'RESTful API for easy integration with existing systems',
        
        'feature-analytics-title': 'Advanced Analytics',
        'feature-analytics-desc': 'Real-time reporting and business intelligence',
        
        // Screenshots section
        'screenshots-title': 'System Preview',
        'screenshots-subtitle': 'See Ultra ERP in action',
        
        // Technology section
        'tech-title': 'Modern Technology Stack',
        'tech-subtitle': 'Built on proven and cutting-edge technologies',
        
        'tech-symfony-title': 'Symfony Framework',
        'tech-symfony-desc': 'Robust PHP framework for enterprise applications',
        
        'tech-ai-title': 'AI & Machine Learning',
        'tech-ai-desc': 'Advanced algorithms for intelligent automation',
        
        'tech-security-title': 'Advanced Security',
        'tech-security-desc': 'Multi-layer security with encryption and monitoring',
        
        'tech-performance-title': 'High Performance',
        'tech-performance-desc': 'Optimized for speed and scalability',
        
        // CTA section
        'cta-title': 'Ready to Transform Your Business?',
        'cta-subtitle': 'Join companies that have already chosen Ultra ERP for their digital transformation',
        'cta-primary': 'Contact Us',
        'cta-secondary': 'Free Consultation',
        
        // Footer
        'footer-product': 'Product',
        'footer-features': 'Features',
        'footer-screenshots': 'Screenshots',
        'footer-technology': 'Technology',
        'footer-documentation': 'Documentation',
        'footer-contact-section': 'Contact',
        'footer-contact': 'Contact',
        'footer-support': 'Support',
        'footer-consultation': 'Consultation',
        'footer-partnership': 'Partnership',
        'footer-career': 'Career',
        'footer-copyright': 'All rights reserved.',
        
        // Modal
        'modal-title': 'System in Development',
        'modal-description': 'Ultra ERP is currently in active development. We are working on completing all functionalities and optimizing the system for enterprise deployment.',
        'modal-info': 'The system is not yet for sale.',
        'modal-info-desc': 'Follow our website for current information about availability and beta testing opportunities.',
        'modal-button': 'I Understand'
    },
    
    cz: {
        // Navigation
        'nav-features': 'Vlastnosti',
        'nav-screenshots': 'Screenshoty',
        'nav-technology': 'Technologie', 
        'nav-contact': 'Kontakt',
        
        // Hero section
        'hero-title': 'Revolučný modulární ERP systém',
        'hero-subtitle': 'Enterprise řešení nové generace kombinující sílu umělé inteligence s enterprise-grade bezpečností a flexibilitou',
        'hero-cta-primary': 'Kontaktovat nás',
        'hero-cta-secondary': 'Nezávazná konzultace',
        
        // Features section
        'features-title': 'Klíčové vlastnosti',
        'features-subtitle': 'Objevte, co dělá Ultra ERP jedinečným',
        
        'feature-ai-title': 'AI Integrace',
        'feature-ai-desc': 'Pokročilá umělá inteligence pro automatizaci procesů a inteligentní analytiku',
        
        'feature-modular-title': 'Modulární architektura',
        'feature-modular-desc': 'Flexibilní systém, který se přizpůsobí potřebám vašeho podnikání',
        
        'feature-security-title': 'Enterprise bezpečnost',
        'feature-security-desc': 'Bankovní úroveň zabezpečení s pokročilým šifrováním a řízením přístupu',
        
        'feature-cloud-title': 'Cloud & On-Premise',
        'feature-cloud-desc': 'Nasaďte v cloudu nebo na vlastní infrastruktuře',
        
        'feature-api-title': 'Moderní API',
        'feature-api-desc': 'RESTful API pro snadnou integraci s existujícími systémy',
        
        'feature-analytics-title': 'Pokročilá analytika',
        'feature-analytics-desc': 'Real-time reportování a business intelligence',
        
        // Screenshots section
        'screenshots-title': 'Náhled systému',
        'screenshots-subtitle': 'Podívejte se na Ultra ERP v akci',
        
        // Technology section
        'tech-title': 'Moderní technologický stack',
        'tech-subtitle': 'Postavený na ověřených a špičkových technologiích',
        
        'tech-symfony-title': 'Symfony Framework',
        'tech-symfony-desc': 'Robustní PHP framework pro enterprise aplikace',
        
        'tech-ai-title': 'AI & Machine Learning',
        'tech-ai-desc': 'Pokročilé algoritmy pro inteligentní automatizaci',
        
        'tech-security-title': 'Pokročilá bezpečnost',
        'tech-security-desc': 'Vícevrstvá bezpečnost s šifrováním a monitoringem',
        
        'tech-performance-title': 'Vysoký výkon',
        'tech-performance-desc': 'Optimalizováno pro rychlost a škálovatelnost',
        
        // CTA section
        'cta-title': 'Připraveni transformovat své podnikání?',
        'cta-subtitle': 'Připojte se ke společnostem, které si již vybraly Ultra ERP pro svou digitální transformaci',
        'cta-primary': 'Kontaktovat nás',
        'cta-secondary': 'Nezávazná konzultace',
        
        // Footer
        'footer-product': 'Produkt',
        'footer-features': 'Vlastnosti',
        'footer-screenshots': 'Screenshoty',
        'footer-technology': 'Technologie',
        'footer-documentation': 'Dokumentace',
        'footer-contact-section': 'Kontakt',
        'footer-contact': 'Kontakt',
        'footer-support': 'Podpora',
        'footer-consultation': 'Konzultace',
        'footer-partnership': 'Partnerství',
        'footer-career': 'Kariéra',
        'footer-copyright': 'Všechna práva vyhrazena.',
        
        // Modal
        'modal-title': 'Systém v přípravě',
        'modal-description': 'Ultra ERP je momentálně ve fázi aktivního vývoje. Pracujeme na dokončení všech funkcionalit a optimalizaci systému pro enterprise nasazení.',
        'modal-info': 'Systém zatím není v prodeji.',
        'modal-info-desc': 'Sledujte naše stránky pro aktuální informace o dostupnosti a možnostech beta testování.',
        'modal-button': 'Rozumím'
    }
};

// Current language (default: English)
let currentLanguage = 'en';

// Get translation for key
function getTranslation(key) {
    return translations[currentLanguage][key] || key;
}

// Update all translatable elements
function updateTranslations() {
    const elements = document.querySelectorAll('[data-translate]');
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        const translation = getTranslation(key);
        
        if (element.tagName === 'INPUT' && element.type === 'submit') {
            element.value = translation;
        } else {
            element.textContent = translation;
        }
    });
    
    // Update current year
    const currentYearElement = document.getElementById('currentYear');
    if (currentYearElement) {
        const currentYear = new Date().getFullYear();
        currentYearElement.textContent = currentYear;
    }
}

// Change language
function changeLanguage(lang) {
    if (translations[lang]) {
        currentLanguage = lang;
        localStorage.setItem('ultra-erp-language', lang);
        updateTranslations();
        updateLanguageSelector();
    }
}

// Update language selector active state
function updateLanguageSelector() {
    const selectors = document.querySelectorAll('.lang-selector');
    selectors.forEach(selector => {
        const buttons = selector.querySelectorAll('button');
        buttons.forEach(button => {
            const lang = button.getAttribute('data-lang');
            if (lang === currentLanguage) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    });
}

// Initialize multilang
function initMultilang() {
    // Load saved language or default to English
    const savedLanguage = localStorage.getItem('ultra-erp-language') || 'en';
    currentLanguage = savedLanguage;
    
    // Make functions globally available
    window.changeLanguage = changeLanguage;
    
    // Update translations on page load
    updateTranslations();
    updateLanguageSelector();
}

// Export functions
export { initMultilang, changeLanguage, getTranslation };
