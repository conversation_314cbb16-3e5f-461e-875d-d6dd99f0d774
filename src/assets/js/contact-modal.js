// Contact modal functionality
function openContactModal() {
    const modal = document.getElementById('contactModal');
    if (!modal) return;
    
    // Show modal
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Add active class after a brief delay for smooth animation
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);
}

function closeContactModal() {
    const modal = document.getElementById('contactModal');
    if (!modal) return;
    
    // Remove active class for smooth closing
    modal.classList.remove('active');
    
    // Hide modal after animation completes
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }, 300);
}

// Close modal when pressing Escape key
function handleEscapeKey(event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('contactModal');
        if (modal && modal.classList.contains('active')) {
            closeContactModal();
        }
    }
}

export function initContactModal() {
    // Make functions globally available
    window.openContactModal = openContactModal;
    window.closeContactModal = closeContactModal;
    
    // Add escape key listener
    document.addEventListener('keydown', handleEscapeKey);
    
    // Prevent modal content clicks from closing the modal
    const modalContent = document.querySelector('.contact-modal-content');
    if (modalContent) {
        modalContent.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
}
