// Gallery functionality
const galleryImages = [
    {
        src: 'assets/images/main-dashboard.png',
        title: 'Hlavní dashboard'
    },
    {
        src: 'assets/images/storage-component.png',
        title: 'Skladový management systém'
    },
    {
        src: 'assets/images/ai-agent-component.png',
        title: 'AI Agent Intelligence Platform'
    },
    {
        src: 'assets/images/erp-settings.png',
        title: 'Enterprise Administration Center'
    },
    {
        src: 'assets/images/custom-component-example.png',
        title: 'Business Process Management Module'
    },
    {
        src: 'assets/images/custom-component-example-2.png',
        title: 'Advanced Enterprise Module'
    }
];

let currentImageIndex = 0;

function openGallery(index) {
    currentImageIndex = index;
    const modal = document.getElementById('galleryModal');
    const image = document.getElementById('galleryImage');
    const title = document.getElementById('galleryTitle');
    const description = document.getElementById('galleryDescription');

    const currentImage = galleryImages[index];

    // Set image source and content
    image.src = currentImage.src;
    image.alt = currentImage.title;
    title.textContent = currentImage.title;
    description.textContent = currentImage.description;

    // Show modal with smooth animation
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // Add active class after a brief delay for smooth animation
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);
}

function closeGallery() {
    const modal = document.getElementById('galleryModal');

    // Remove active class for smooth closing
    modal.classList.remove('active');

    // Hide modal after animation completes
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }, 300);
}

function nextImage() {
    currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
    updateGalleryImage();
}

function prevImage() {
    currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
    updateGalleryImage();
}

function updateGalleryImage() {
    const image = document.getElementById('galleryImage');
    const title = document.getElementById('galleryTitle');
    const description = document.getElementById('galleryDescription');

    // Add changing animation
    image.classList.add('changing');

    // Update content after brief delay
    setTimeout(() => {
        const currentImage = galleryImages[currentImageIndex];
        image.src = currentImage.src;
        image.alt = currentImage.title;
        title.textContent = currentImage.title;
        description.textContent = currentImage.description;

        // Remove changing class after animation
        setTimeout(() => {
            image.classList.remove('changing');
        }, 200);
    }, 100);
}

export function initGallery() {
    // Gallery event listeners
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            closeGallery();
        } else if (e.key === 'ArrowRight') {
            nextImage();
        } else if (e.key === 'ArrowLeft') {
            prevImage();
        }
    });

    // Close gallery when clicking outside the image
    const galleryModal = document.getElementById('galleryModal');
    if (galleryModal) {
        galleryModal.addEventListener('click', function (e) {
            if (e.target === this) {
                closeGallery();
            }
        });
    }

    // Make functions globally available for HTML onclick handlers
    window.openGallery = openGallery;
    window.closeGallery = closeGallery;
    window.nextImage = nextImage;
    window.prevImage = prevImage;
}
