/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(20, 20, 30, 0.9);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  border-radius: 6px;
  border: 2px solid rgba(20, 20, 30, 0.9);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1, #ec4899);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.5);
  transform: scale(1.05);
}

::-webkit-scrollbar-corner {
  background: rgba(20, 20, 30, 0.9);
}

/* Firefox scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: #667eea rgba(20, 20, 30, 0.9);
}
