/* Import Tailwind CSS */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Font Awesome */
@import '@fortawesome/fontawesome-free/css/all.css';

/* Import AOS */
@import 'aos/dist/aos.css';

/* Import custom CSS modules */
@import './scrollbar.css';
@import './responsive.css';
@import './animations.css';

/* Custom CSS Variables */
:root {
  --gradient-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  --gradient-dark: linear-gradient(135deg, #2d1b69 0%, #11998e 100%);
  --color-primary: #11998e;
  --color-secondary: #667eea;
  --color-accent: #764ba2;
}

/* Base Styles */
.gradient-bg {
  background: var(--gradient-primary);
}

.dark-gradient {
  background: var(--gradient-dark);
}

/* Feature Cards */
.feature-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.feature-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 0;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover::after {
  opacity: 1;
}

.feature-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.feature-icon {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 3;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.feature-card:hover .feature-icon {
  transform: scale(1.15) rotate(8deg);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.feature-content {
  position: relative;
  z-index: 2;
}

/* Enhanced card animations */
.feature-card {
  will-change: transform, box-shadow;
}

.feature-card:hover .feature-content {
  transform: translateZ(0);
}

/* Improved badge styling */
.feature-card .bg-blue-500\/15,
.feature-card .bg-green-500\/15,
.feature-card .bg-purple-500\/15,
.feature-card .bg-red-500\/15,
.feature-card .bg-yellow-500\/15,
.feature-card .bg-indigo-500\/15,
.feature-card .bg-pink-500\/15,
.feature-card .bg-cyan-500\/15,
.feature-card .bg-teal-500\/15,
.feature-card .bg-emerald-500\/15 {
  transition: all 0.3s ease;
}

.feature-card:hover .bg-blue-500\/15,
.feature-card:hover .bg-green-500\/15,
.feature-card:hover .bg-purple-500\/15,
.feature-card:hover .bg-red-500\/15,
.feature-card:hover .bg-yellow-500\/15,
.feature-card:hover .bg-indigo-500\/15,
.feature-card:hover .bg-pink-500\/15,
.feature-card:hover .bg-cyan-500\/15,
.feature-card:hover .bg-teal-500\/15,
.feature-card:hover .bg-emerald-500\/15 {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Enhanced grid spacing */
.features-grid {
  perspective: 1000px;
}

.feature-card {
  transform-style: preserve-3d;
}

/* Section transitions */
#features {
  position: relative;
}

#features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom,
    rgba(15, 52, 96, 0.8) 0%,
    rgba(17, 24, 39, 0.9) 50%,
    rgba(17, 24, 39, 1) 100%);
  z-index: 1;
}

#features > div {
  position: relative;
  z-index: 2;
}

/* Screenshot containers */
.screenshot-container {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(0);
}

.screenshot-container:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}

.screenshot-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 40%,
    rgba(0, 0, 0, 0.8) 100%
  );
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.screenshot-container:hover .screenshot-overlay {
  opacity: 0.9;
}

.screenshot-expand {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.screenshot-container:hover .screenshot-expand {
  opacity: 1;
  transform: scale(1);
}

/* Navigation styles */
nav {
  transition: transform 0.3s ease-in-out, background-color 0.3s ease;
  will-change: transform;
}

nav.nav-hidden {
  transform: translateY(-100%);
}

/* Scroll to top button */
.scroll-to-top-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #11998e, #667eea);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(17, 153, 142, 0.3);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.scroll-to-top-btn:hover {
  transform: translateY(0) scale(1.1);
  box-shadow: 0 6px 20px rgba(17, 153, 142, 0.5);
}

.scroll-to-top-btn.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

@media (max-width: 768px) {
  .scroll-to-top-btn {
    bottom: 15px;
    right: 15px;
    width: 45px;
    height: 45px;
    font-size: 16px;
  }
}
