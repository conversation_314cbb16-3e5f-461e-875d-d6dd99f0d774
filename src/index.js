// Import CSS
import './assets/css/main.css';

// Import AOS
import AOS from 'aos';

// Import custom modules
import { initGallery } from './assets/js/gallery.js';
import { initNavigation } from './assets/js/navigation.js';
import { initAnimations } from './assets/js/animations.js';
import { initScrollToTop } from './assets/js/scroll-to-top.js';
import { initFeatures } from './assets/js/features.js';
import { initContactModal } from './assets/js/contact-modal.js';

// Initialize AOS
AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    offset: 100
});

// Initialize all modules when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initGallery();
    initNavigation();
    initAnimations();
    initScrollToTop();
    initFeatures();
    initContactModal();
});
