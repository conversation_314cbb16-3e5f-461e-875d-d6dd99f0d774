services:
  ultra-erp-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ultra-erp-presentation-dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    command: npm start
    stdin_open: true
    tty: true
    user: "1001:1001"
    networks:
      - ultra-erp-network

  ultra-erp-build:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ultra-erp-presentation-build
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=production
    command: npm run build
    user: "1001:1001"
    networks:
      - ultra-erp-network

networks:
  ultra-erp-network:
    driver: bridge
