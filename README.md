# ULTRA-ERP Presentation Page

Modern presentation page for ULTRA-ERP system built with webpack, Tailwind CSS, and Docker.

## Features

- **Modern Build System**: Webpack 5 with hot reloading and asset optimization
- **CSS Processing**: PostCSS with Tailwind CSS, autoprefixer, and minification
- **Docker Development**: Complete Docker-based development environment
- **Responsive Design**: Mobile-first responsive design with custom animations
- **Modular Architecture**: Organized CSS and JavaScript modules

## Prerequisites

- Docker and Docker Compose
- Make (optional, for easier command execution)

## Quick Start

### Using Make (Recommended)

```bash
# Install dependencies
make install

# Start development server
make start

# Build production assets
make build

# Clean build artifacts
make clean
```

### Using Docker Compose Directly

```bash
# Install dependencies
docker-compose run --rm ultra-erp-dev npm install

# Start development server
docker-compose up ultra-erp-dev

# Build production assets
docker-compose run --rm ultra-erp-build

# Clean build artifacts
docker-compose run --rm ultra-erp-dev npm run clean
```

### Using npm (if Node.js is installed locally)

```bash
# Install dependencies
npm install

# Start development server
npm start

# Build production assets
npm run build

# Clean build artifacts
npm run clean
```

## Development

The development server runs on `http://localhost:3000` with hot reloading enabled.

### Project Structure

```
├── src/
│   └── index.js              # Main entry point
├── assets/
│   ├── css/
│   │   ├── main.css          # Main CSS file with imports
│   │   ├── animations.css    # Animation styles
│   │   ├── responsive.css    # Responsive styles
│   │   └── scrollbar.css     # Custom scrollbar styles
│   ├── js/
│   │   ├── gallery.js        # Gallery functionality
│   │   ├── navigation.js     # Navigation functionality
│   │   └── animations.js     # Animation functionality
│   └── images/               # Static images
├── dist/                     # Built assets (generated)
├── webpack.config.js         # Webpack configuration
├── tailwind.config.js        # Tailwind CSS configuration
├── postcss.config.js         # PostCSS configuration
├── docker-compose.yml        # Docker services
├── Dockerfile               # Docker image definition
└── Makefile                 # Development commands
```

## Available Scripts

### npm scripts

- `npm start` - Start development server with webpack watch mode
- `npm run build` - Create production build
- `npm run clean` - Remove built/compiled files and clean the build directory
- `npm install` - Install node_modules via Docker

### Docker scripts

- `npm run docker:start` - Start development server via Docker
- `npm run docker:build` - Create production build via Docker
- `npm run docker:install` - Install dependencies via Docker
- `npm run docker:clean` - Clean build artifacts via Docker

## Build Process

### Development Build

- Webpack dev server with hot reloading
- Source maps for debugging
- Unminified assets for faster builds
- CSS injection for instant updates

### Production Build

- Minified and optimized JavaScript
- CSS extraction and minification
- Asset optimization and compression
- Content-based hashing for cache busting

## Technologies Used

- **Build Tool**: Webpack 5
- **CSS Framework**: Tailwind CSS 3
- **CSS Processing**: PostCSS with autoprefixer
- **JavaScript**: ES6+ with Babel transpilation
- **Animations**: AOS (Animate On Scroll)
- **Icons**: Font Awesome 6
- **Container**: Docker with Alpine Linux
- **Development**: Hot reloading with webpack-dev-server

## Browser Support

- Chrome (last 2 versions)
- Firefox (last 2 versions)
- Safari (last 2 versions)
- Edge (last 2 versions)

## Contributing

1. Make changes to source files in `src/` and `assets/`
2. Test changes with `make start`
3. Build production assets with `make build`
4. Verify the build works correctly

## License

MIT License
