/* Screenshot Container Animations */
.screenshot-container {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(17, 153, 142, 0.2);
  transition: all 0.4s ease;
  background: linear-gradient(145deg, rgba(30, 30, 30, 0.9), rgba(20, 20, 20, 0.8));
}

.screenshot-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(17, 153, 142, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.screenshot-container:hover::before {
  opacity: 1;
}

.screenshot-container img {
  transition: transform 0.4s ease;
  position: relative;
  z-index: 0;
}

.screenshot-container:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 25px 60px rgba(17, 153, 142, 0.3), 0 0 40px rgba(17, 153, 142, 0.1);
  border-color: rgba(17, 153, 142, 0.4);
}

.screenshot-container:hover img {
  transform: scale(1.08);
}

.screenshot-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, transparent 100%);
  z-index: 2;
}

.screenshot-expand {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(17, 153, 142, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0.8);
}

.screenshot-container:hover .screenshot-expand {
  opacity: 1;
  transform: scale(1);
}

.screenshot-expand:hover {
  background: rgba(17, 153, 142, 1);
  transform: scale(1.1);
}

/* Tech Badges */
.tech-badge {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark-tech-badge {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Floating Elements */
.floating-element {
  animation: float 6s ease-in-out infinite;
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

.glow-text {
  text-shadow: 0 0 20px rgba(17, 153, 142, 0.5);
}

.hero-title {
  background: linear-gradient(45deg, #00f5ff, #ff6b6b, #4ecdc4, #45b7d1);
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Floating Icons Background */
.floating-icons {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.floating-icon {
  position: absolute;
  opacity: 0.3;
  animation: float-around 15s linear infinite;
}

/* Tech Background Elements */
.tech-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  z-index: 0;
  overflow: hidden;
}

.terminal-window {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #00ff00;
  padding: 10px;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
}

.code-snippet {
  position: absolute;
  background: rgba(30, 30, 30, 0.9);
  border-radius: 6px;
  border-left: 3px solid #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 10px;
  color: #ffffff;
  padding: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
}

.network-diagram {
  position: absolute;
  width: 200px;
  height: 150px;
  opacity: 0.3;
}

.floating-tech {
  animation: float-tech 8s ease-in-out infinite;
}

/* Gallery Modal */
.gallery-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
}

.gallery-modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-content {
  max-width: 85%;
  max-height: 85%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gallery-image {
  max-width: 100%;
  max-height: 70vh;
  width: auto;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  object-fit: contain;
}

.gallery-description {
  margin-top: 20px;
  text-align: center;
  max-width: 600px;
  padding: 0 20px;
}

.gallery-close {
  position: absolute;
  top: -40px;
  right: 0;
  color: white;
  font-size: 30px;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-nav {
  position: absolute;
  top: 35%;
  transform: translateY(-50%);
  color: white;
  font-size: 24px;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.gallery-nav:hover {
  background: rgba(17, 153, 142, 0.8);
}

.gallery-prev {
  left: -60px;
}

.gallery-next {
  right: -60px;
}

@media (max-width: 768px) {
  .gallery-content {
    max-width: 95%;
    max-height: 90%;
  }

  .gallery-image {
    max-height: 60vh;
  }

  .gallery-description {
    margin-top: 15px;
    padding: 0 15px;
  }

  .gallery-nav {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .gallery-prev {
    left: -45px;
  }

  .gallery-next {
    right: -45px;
  }
}
