/* Responsive feature cards */
@media (max-width: 1280px) {
  .features-grid {
    gap: 1.5rem !important;
  }
}

@media (max-width: 1024px) {
  .feature-card {
    padding: 2rem !important;
  }

  .feature-icon {
    width: 6rem !important;
    height: 6rem !important;
    margin-bottom: 1.5rem !important;
  }

  .feature-icon i {
    font-size: 2.5rem !important;
  }

  .feature-card h3 {
    font-size: 1.875rem !important;
    margin-bottom: 1rem !important;
  }

  .feature-card p {
    font-size: 1rem !important;
    margin-bottom: 1.5rem !important;
  }

  .features-grid {
    gap: 1.25rem !important;
  }
}

/* Medium screens - better 2-column layout */
@media (max-width: 900px) and (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1rem !important;
  }
}

/* Small-medium screens optimization */
@media (max-width: 850px) {
  .features-grid {
    gap: 1rem !important;
  }

  .feature-card {
    padding: 1.75rem !important;
  }
}

/* Hero cards responsive */
@media (max-width: 1023px) and (min-width: 640px) {
  .grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1.5rem !important;
  }
}

@media (max-width: 639px) {
  .grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: repeat(1, 1fr) !important;
    gap: 1rem !important;
  }
}

@media (max-width: 768px) {
  .feature-card {
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  .feature-icon {
    width: 4rem !important;
    height: 4rem !important;
    margin-bottom: 1rem !important;
  }

  .feature-icon i {
    font-size: 2rem !important;
  }

  .feature-card h3 {
    font-size: 1.5rem !important;
    margin-bottom: 0.75rem !important;
  }

  .feature-card p {
    font-size: 1rem !important;
    margin-bottom: 1rem !important;
    line-height: 1.5 !important;
  }

  .feature-card .flex.flex-wrap {
    gap: 0.5rem !important;
  }

  .feature-card .flex.flex-wrap span {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  /* Grid adjustments for tablets */
  .features-grid {
    gap: 1rem !important;
  }
}

@media (max-width: 640px) {
  .feature-card {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .feature-icon {
    width: 3rem !important;
    height: 3rem !important;
    margin-bottom: 0.75rem !important;
  }

  .feature-icon i {
    font-size: 1.5rem !important;
  }

  .feature-card h3 {
    font-size: 1.125rem !important;
    margin-bottom: 0.5rem !important;
  }

  .feature-card p {
    font-size: 0.875rem !important;
    line-height: 1.4 !important;
    margin-bottom: 0.75rem !important;
  }

  .feature-card .flex.flex-wrap {
    gap: 0.25rem !important;
    justify-content: flex-start !important;
  }

  .feature-card .flex.flex-wrap span {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.7rem !important;
    flex-shrink: 0;
  }

  /* Features section spacing */
  #features {
    padding: 2rem 0 !important;
  }

  #features .max-w-7xl {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  #features .text-center.mb-16 {
    margin-bottom: 1.5rem !important;
  }

  #features h2 {
    font-size: 1.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  #features .text-xl {
    font-size: 1rem !important;
  }

  /* Grid adjustments for mobile - no gaps between cards */
  .features-grid {
    gap: 0.5rem !important;
  }
}

@media (max-width: 480px) {
  .feature-card {
    padding: 0.875rem !important;
    margin-bottom: 0.75rem !important;
  }

  .feature-icon {
    width: 2.75rem !important;
    height: 2.75rem !important;
    margin-bottom: 0.5rem !important;
  }

  .feature-icon i {
    font-size: 1.25rem !important;
  }

  .feature-card h3 {
    font-size: 1rem !important;
    margin-bottom: 0.375rem !important;
  }

  .feature-card p {
    font-size: 0.8rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.5rem !important;
  }

  .feature-card .flex.flex-wrap {
    gap: 0.125rem !important;
    justify-content: flex-start !important;
  }

  .feature-card .flex.flex-wrap span {
    padding: 0.125rem 0.375rem !important;
    font-size: 0.625rem !important;
    flex-shrink: 0;
  }

  /* Features section spacing */
  #features {
    padding: 1.5rem 0 !important;
  }

  #features .max-w-7xl {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  #features .text-center.mb-16 {
    margin-bottom: 1rem !important;
  }

  #features h2 {
    font-size: 1.375rem !important;
    margin-bottom: 0.5rem !important;
  }

  #features .text-xl {
    font-size: 0.9rem !important;
  }

  /* Grid adjustments for small mobile - minimal gaps */
  .features-grid {
    gap: 0.375rem !important;
  }
}

/* Technology section responsive */
@media (max-width: 640px) {
  #technology .grid {
    gap: 1rem !important;
  }

  #technology .w-20 {
    width: 3.5rem !important;
    height: 3.5rem !important;
  }

  #technology .text-3xl {
    font-size: 1.5rem !important;
  }

  #technology h3 {
    font-size: 0.9rem !important;
  }

  #technology .text-sm {
    font-size: 0.75rem !important;
  }
}

@media (max-width: 480px) {
  #technology .grid {
    gap: 0.5rem !important;
  }

  #technology .w-20 {
    width: 2.75rem !important;
    height: 2.75rem !important;
  }

  #technology .text-3xl {
    font-size: 1.125rem !important;
  }

  #technology h3 {
    font-size: 0.75rem !important;
  }

  #technology .text-sm {
    font-size: 0.65rem !important;
  }
}

/* Extra small screens - minimize all gaps */
@media (max-width: 360px) {
  .features-grid {
    gap: 0.25rem !important;
  }

  .feature-card {
    padding: 0.75rem !important;
    margin-bottom: 0.5rem !important;
  }

  .feature-icon {
    width: 2.5rem !important;
    height: 2.5rem !important;
    margin-bottom: 0.375rem !important;
  }

  .feature-icon i {
    font-size: 1.125rem !important;
  }

  .feature-card h3 {
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
  }

  .feature-card p {
    font-size: 0.75rem !important;
    line-height: 1.25 !important;
    margin-bottom: 0.375rem !important;
  }

  .feature-card .flex.flex-wrap span {
    padding: 0.125rem 0.25rem !important;
    font-size: 0.6rem !important;
  }

  #technology .grid {
    gap: 0.25rem !important;
  }

  #technology .w-20 {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }

  #technology .text-3xl {
    font-size: 1rem !important;
  }

  #technology h3 {
    font-size: 0.7rem !important;
  }

  #technology .text-sm {
    font-size: 0.6rem !important;
  }
}
